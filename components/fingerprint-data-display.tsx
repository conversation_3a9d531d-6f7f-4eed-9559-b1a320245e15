"use client";

import { EnhancedFingerprintProData } from "@/types";
import { Shield, Globe, Monitor, Eye, AlertTriangle, CheckCircle, History } from "lucide-react";

interface FingerprintDataDisplayProps {
  data: EnhancedFingerprintProData;
}

export function FingerprintDataDisplay({ data }: FingerprintDataDisplayProps) {
  const { clientData, serverData, isLoading, hasServerData } = data;

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-300 rounded w-3/4 mb-2"></div>
          <div className="h-4 bg-gray-300 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  if (clientData?.error) {
    return (
      <div className="bg-red-50 dark:bg-red-900 border border-red-200 dark:border-red-800 rounded-lg p-4">
        <div className="flex items-center gap-2 text-red-700 dark:text-red-300">
          <AlertTriangle className="h-5 w-5" />
          <h3 className="font-semibold">Fingerprint Pro 数据获取失败</h3>
        </div>
        <p className="text-red-600 dark:text-red-400 mt-2">{clientData.error.message}</p>
      </div>
    );
  }

  if (!clientData?.result) {
    return (
      <div className="bg-yellow-50 dark:bg-yellow-900 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
        <div className="flex items-center gap-2 text-yellow-700 dark:text-yellow-300">
          <AlertTriangle className="h-5 w-5" />
          <h3 className="font-semibold">等待 Fingerprint Pro 数据...</h3>
        </div>
      </div>
    );
  }

  const result = clientData.result;

  return (
    <div className="space-y-6">
      {/* 基础信息 */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-2 mb-4">
          <Eye className="h-5 w-5 text-blue-500" />
          <h3 className="text-lg font-semibold">访问者识别</h3>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="text-sm font-medium text-gray-500 dark:text-gray-400">访问者 ID</label>
            <p className="font-mono text-sm break-all">{result.visitorId}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500 dark:text-gray-400">请求 ID</label>
            <p className="font-mono text-xs text-gray-600 dark:text-gray-400 break-all">{result.requestId}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500 dark:text-gray-400">置信度</label>
            <p className={`font-semibold ${
              result.confidence?.score >= 0.9 ? 'text-green-600' :
              result.confidence?.score >= 0.7 ? 'text-yellow-600' :
              'text-red-600'
            }`}>
              {result.confidence ? `${(result.confidence.score * 100).toFixed(1)}%` : '未知'}
            </p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500 dark:text-gray-400">访问者状态</label>
            <p className="flex items-center gap-1">
              {result.visitorFound ? (
                <>
                  <CheckCircle className="h-4 w-4 text-green-500" />
                  <span className="text-green-600">已知访问者</span>
                </>
              ) : (
                <>
                  <AlertTriangle className="h-4 w-4 text-blue-500" />
                  <span className="text-blue-600">新访问者</span>
                </>
              )}
            </p>
          </div>
        </div>
      </div>

      {/* IP和地理位置信息 */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-2 mb-4">
          <Globe className="h-5 w-5 text-green-500" />
          <h3 className="text-lg font-semibold">IP 和地理位置</h3>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="text-sm font-medium text-gray-500 dark:text-gray-400">IP 地址</label>
            <p className="font-mono">{result.ip || '未知'}</p>
          </div>
          {result.ipLocation && (
            <>
              <div>
                <label className="text-sm font-medium text-gray-500 dark:text-gray-400">国家</label>
                <p>{result.ipLocation.country?.name || '未知'}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500 dark:text-gray-400">城市</label>
                <p>{result.ipLocation.city?.name || '未知'}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500 dark:text-gray-400">时区</label>
                <p>{result.ipLocation.timezone || '未知'}</p>
              </div>
              {result.ipLocation.latitude && result.ipLocation.longitude && (
                <div>
                  <label className="text-sm font-medium text-gray-500 dark:text-gray-400">坐标</label>
                  <p className="font-mono text-sm">
                    {result.ipLocation.latitude.toFixed(4)}, {result.ipLocation.longitude.toFixed(4)}
                  </p>
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* 设备和浏览器信息 */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-2 mb-4">
          <Monitor className="h-5 w-5 text-purple-500" />
          <h3 className="text-lg font-semibold">设备和浏览器</h3>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="text-sm font-medium text-gray-500 dark:text-gray-400">操作系统</label>
            <p>{result.os?.name} {result.os?.version}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500 dark:text-gray-400">浏览器</label>
            <p>{result.browser?.name} {result.browser?.version}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500 dark:text-gray-400">设备</label>
            <p>{result.device || '未知'}</p>
          </div>
          <div>
            <label className="text-sm font-medium text-gray-500 dark:text-gray-400">隐身模式</label>
            <p className={result.incognito ? 'text-yellow-600' : 'text-green-600'}>
              {result.incognito ? '是' : '否'}
            </p>
          </div>
        </div>
      </div>

      {/* 安全检测 */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
        <div className="flex items-center gap-2 mb-4">
          <Shield className="h-5 w-5 text-red-500" />
          <h3 className="text-lg font-semibold">安全检测</h3>
          {!hasServerData && (
            <span className="text-xs bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">基础检测</span>
          )}
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {/* 客户端检测 */}
          <div>
            <label className="text-sm font-medium text-gray-500 dark:text-gray-400">隐身模式</label>
            <p className={result.incognito ? 'text-yellow-600' : 'text-green-600'}>
              {result.incognito ? '检测到' : '未检测到'}
            </p>
          </div>

          {/* 服务端增强检测 */}
          {hasServerData && serverData?.currentVisit && (
            <>
              <div>
                <label className="text-sm font-medium text-gray-500 dark:text-gray-400">VPN</label>
                <p className={serverData.currentVisit.vpn?.result ? 'text-red-600' : 'text-green-600'}>
                  {serverData.currentVisit.vpn?.result ? '检测到' : '未检测到'}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500 dark:text-gray-400">代理</label>
                <p className={serverData.currentVisit.proxy?.result ? 'text-red-600' : 'text-green-600'}>
                  {serverData.currentVisit.proxy?.result ? '检测到' : '未检测到'}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500 dark:text-gray-400">Tor</label>
                <p className={serverData.currentVisit.tor?.result ? 'text-red-600' : 'text-green-600'}>
                  {serverData.currentVisit.tor?.result ? '检测到' : '未检测到'}
                </p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500 dark:text-gray-400">篡改检测</label>
                <p className={serverData.currentVisit.tampering?.result ? 'text-red-600' : 'text-green-600'}>
                  {serverData.currentVisit.tampering?.result ? '检测到' : '未检测到'}
                </p>
              </div>
              {serverData.currentVisit.suspectScore !== undefined && (
                <div>
                  <label className="text-sm font-medium text-gray-500 dark:text-gray-400">可疑评分</label>
                  <p className={
                    serverData.currentVisit.suspectScore > 50 ? 'text-red-600' :
                    serverData.currentVisit.suspectScore > 20 ? 'text-yellow-600' :
                    'text-green-600'
                  }>
                    {serverData.currentVisit.suspectScore}/100
                  </p>
                </div>
              )}
            </>
          )}
        </div>
      </div>

      {/* 访问历史 */}
      {hasServerData && serverData?.visitorHistory && serverData.visitorHistory.visits.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700">
          <div className="flex items-center gap-2 mb-4">
            <History className="h-5 w-5 text-indigo-500" />
            <h3 className="text-lg font-semibold">访问历史</h3>
          </div>
          
          <div className="space-y-3">
            {serverData.visitorHistory.visits.slice(0, 3).map((visit) => (
              <div key={visit.requestId} className="bg-gray-50 dark:bg-gray-700 rounded p-3">
                <div className="flex justify-between items-start">
                  <div>
                    <p className="text-sm font-medium">
                      {new Date(visit.timestamp * 1000).toLocaleString()}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      {visit.browserDetails?.browserName} - {visit.ip}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      置信度: {(visit.confidence.score * 100).toFixed(1)}%
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}