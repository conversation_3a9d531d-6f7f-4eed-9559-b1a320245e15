"use client";

import { Copy, Check } from "lucide-react";
import { useState } from "react";
import { cn } from "@/lib/utils";

interface InfoCardProps {
  title: string;
  children: React.ReactNode;
  className?: string;
}

interface InfoItemProps {
  label: string;
  value: string | number | null | undefined;
  copyable?: boolean;
  className?: string;
}

export function InfoCard({ title, children, className }: InfoCardProps) {
  return (
    <div className={cn(
      "bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm border border-gray-200 dark:border-gray-700",
      className
    )}>
      <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
        {title}
      </h3>
      <div className="space-y-3">
        {children}
      </div>
    </div>
  );
}

export function InfoItem({ label, value, copyable = false, className }: InfoItemProps) {
  const [copied, setCopied] = useState(false);

  const handleCopy = async () => {
    if (!value) return;
    
    try {
      await navigator.clipboard.writeText(String(value));
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('复制失败:', err);
    }
  };

  if (value === null || value === undefined || value === '') {
    return null;
  }

  return (
    <div className={cn("flex items-center justify-between", className)}>
      <div className="flex-1">
        <span className="text-sm text-gray-600 dark:text-gray-400">{label}:</span>
        <span className="ml-2 text-sm font-medium text-gray-900 dark:text-gray-100 break-all">
          {value}
        </span>
      </div>
      {copyable && (
        <button
          onClick={handleCopy}
          className="ml-2 p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
          title="复制"
        >
          {copied ? (
            <Check className="h-4 w-4 text-green-500" />
          ) : (
            <Copy className="h-4 w-4 text-gray-500" />
          )}
        </button>
      )}
    </div>
  );
}