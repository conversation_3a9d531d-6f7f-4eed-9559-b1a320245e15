"use client";

import { InfoCard, InfoItem } from "./info-card";
import { HTTPHeaders, WebRTCInfo, SystemHardwareInfo, HTML5Features, BatteryInfo } from "@/types";
import { 
  Network, 
  Router, 
  Cpu, 
  Code, 
  Battery, 
  AlertTriangle,
  CheckCircle,
  XCircle
} from "lucide-react";

interface HTTPHeadersCardProps {
  headers: HTTPHeaders | null;
  loading?: boolean;
}

export function HTTPHeadersCard({ headers, loading }: HTTPHeadersCardProps) {
  return (
    <InfoCard title="HTTP请求头">
      <div className="flex items-center gap-2 mb-4">
        <Network className="h-5 w-5 text-blue-500" />
        <span className="font-medium">浏览器发送的请求头信息</span>
      </div>
      {loading ? (
        <div className="animate-pulse space-y-3">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
          ))}
        </div>
      ) : headers ? (
        <div className="space-y-3 max-h-80 overflow-y-auto">
          <InfoItem label="User-Agent" value={headers.userAgent} copyable />
          <InfoItem label="Accept" value={headers.accept} copyable />
          <InfoItem label="Accept-Language" value={headers.acceptLanguage} copyable />
          <InfoItem label="Accept-Encoding" value={headers.acceptEncoding} copyable />
          <InfoItem label="Connection" value={headers.connection} copyable />
          <InfoItem label="Host" value={headers.host} copyable />
          <InfoItem label="Referer" value={headers.referer} copyable />
          <InfoItem label="Origin" value={headers.origin} copyable />
          <InfoItem label="Sec-Fetch-Site" value={headers.secFetchSite} copyable />
          <InfoItem label="Sec-Fetch-Mode" value={headers.secFetchMode} copyable />
          <InfoItem label="DNT" value={headers.dnt} copyable />
          
          {headers.rawHeaders && Object.keys(headers.rawHeaders).length > 0 && (
            <details className="mt-4">
              <summary className="cursor-pointer text-sm font-medium text-gray-700 dark:text-gray-300">
                所有请求头 ({Object.keys(headers.rawHeaders).length}个)
              </summary>
              <div className="mt-2 space-y-2 max-h-40 overflow-y-auto">
                {Object.entries(headers.rawHeaders).map(([key, value]) => (
                  <InfoItem key={key} label={key} value={value} copyable />
                ))}
              </div>
            </details>
          )}
        </div>
      ) : (
        <p className="text-gray-500 dark:text-gray-400">无法获取请求头信息</p>
      )}
    </InfoCard>
  );
}

interface WebRTCCardProps {
  webrtc: WebRTCInfo | null;
  loading?: boolean;
}

export function WebRTCCard({ webrtc, loading }: WebRTCCardProps) {
  return (
    <InfoCard title="WebRTC信息">
      <div className="flex items-center gap-2 mb-4">
        <Router className="h-5 w-5 text-purple-500" />
        <span className="font-medium">实时通信技术检测</span>
        {webrtc?.leakDetected && (
          <AlertTriangle className="h-4 w-4 text-amber-500" />
        )}
      </div>
      {loading ? (
        <div className="animate-pulse space-y-3">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
          ))}
        </div>
      ) : webrtc ? (
        <div className="space-y-3">
          <InfoItem 
            label="WebRTC支持" 
            value={webrtc.supported ? "支持" : "不支持"} 
          />
          {webrtc.supported && (
            <>
              <InfoItem 
                label="本地IP地址" 
                value={webrtc.localIPs.length > 0 ? webrtc.localIPs.join(", ") : "未检测到"} 
                copyable 
              />
              <InfoItem 
                label="公网IP地址" 
                value={webrtc.publicIPs.length > 0 ? webrtc.publicIPs.join(", ") : "未检测到"} 
                copyable 
              />
              <InfoItem 
                label="IP泄露风险" 
                value={webrtc.leakDetected ? "是" : "否"} 
              />
              <InfoItem 
                label="ICE候选数量" 
                value={webrtc.candidates.length} 
              />
              
              {webrtc.candidates.length > 0 && (
                <details className="mt-4">
                  <summary className="cursor-pointer text-sm font-medium text-gray-700 dark:text-gray-300">
                    ICE候选详情 ({webrtc.candidates.length}个)
                  </summary>
                  <div className="mt-2 space-y-1 max-h-40 overflow-y-auto text-xs text-gray-600 dark:text-gray-400">
                    {webrtc.candidates.map((candidate, index) => (
                      <div key={index} className="break-all">
                        {candidate.candidate}
                      </div>
                    ))}
                  </div>
                </details>
              )}
            </>
          )}
        </div>
      ) : (
        <p className="text-gray-500 dark:text-gray-400">WebRTC信息加载中...</p>
      )}
    </InfoCard>
  );
}

interface SystemHardwareCardProps {
  systemInfo: SystemHardwareInfo | null;
  loading?: boolean;
}

export function SystemHardwareCard({ systemInfo, loading }: SystemHardwareCardProps) {
  return (
    <InfoCard title="系统硬件信息">
      <div className="flex items-center gap-2 mb-4">
        <Cpu className="h-5 w-5 text-green-500" />
        <span className="font-medium">底层系统和硬件特性</span>
      </div>
      {loading ? (
        <div className="animate-pulse space-y-3">
          {[...Array(6)].map((_, i) => (
            <div key={i} className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
          ))}
        </div>
      ) : systemInfo ? (
        <div className="space-y-3">
          <InfoItem label="平台" value={systemInfo.platform} copyable />
          <InfoItem label="CPU类型" value={systemInfo.cpuClass} copyable />
          <InfoItem label="OS CPU" value={systemInfo.oscpu} copyable />
          <InfoItem label="产品" value={systemInfo.product} copyable />
          <InfoItem label="厂商" value={systemInfo.vendor} copyable />
          <InfoItem label="最大触摸点" value={systemInfo.maxTouchPoints} />
          <InfoItem label="媒体设备数量" value={systemInfo.mediaDevices} />
          <InfoItem label="蓝牙支持" value={systemInfo.bluetooth ? "支持" : "不支持"} />
          <InfoItem label="USB支持" value={systemInfo.usb ? "支持" : "不支持"} />
        </div>
      ) : (
        <p className="text-gray-500 dark:text-gray-400">系统信息加载中...</p>
      )}
    </InfoCard>
  );
}

interface HTML5FeaturesCardProps {
  features: HTML5Features | null;
  loading?: boolean;
}

export function HTML5FeaturesCard({ features, loading }: HTML5FeaturesCardProps) {
  const getFeatureIcon = (supported: boolean) => 
    supported ? <CheckCircle className="h-4 w-4 text-green-500" /> : <XCircle className="h-4 w-4 text-red-500" />;

  return (
    <InfoCard title="HTML5特性支持" className="lg:col-span-2">
      <div className="flex items-center gap-2 mb-4">
        <Code className="h-5 w-5 text-orange-500" />
        <span className="font-medium">现代Web API和特性</span>
      </div>
      {loading ? (
        <div className="animate-pulse space-y-3">
          {[...Array(8)].map((_, i) => (
            <div key={i} className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
          ))}
        </div>
      ) : features ? (
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <h4 className="font-medium text-sm text-gray-700 dark:text-gray-300">存储技术</h4>
            <div className="space-y-1 text-sm">
              <div className="flex items-center gap-2">
                {getFeatureIcon(features.localStorage)}
                <span>LocalStorage</span>
              </div>
              <div className="flex items-center gap-2">
                {getFeatureIcon(features.sessionStorage)}
                <span>SessionStorage</span>
              </div>
              <div className="flex items-center gap-2">
                {getFeatureIcon(features.indexedDB)}
                <span>IndexedDB</span>
              </div>
              <div className="flex items-center gap-2">
                {getFeatureIcon(features.webSQL)}
                <span>WebSQL</span>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <h4 className="font-medium text-sm text-gray-700 dark:text-gray-300">图形技术</h4>
            <div className="space-y-1 text-sm">
              <div className="flex items-center gap-2">
                {getFeatureIcon(features.canvas)}
                <span>Canvas</span>
              </div>
              <div className="flex items-center gap-2">
                {getFeatureIcon(features.svg)}
                <span>SVG</span>
              </div>
              <div className="flex items-center gap-2">
                {getFeatureIcon(features.webGL)}
                <span>WebGL</span>
              </div>
              <div className="flex items-center gap-2">
                {getFeatureIcon(features.webGL2)}
                <span>WebGL2</span>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <h4 className="font-medium text-sm text-gray-700 dark:text-gray-300">多线程技术</h4>
            <div className="space-y-1 text-sm">
              <div className="flex items-center gap-2">
                {getFeatureIcon(features.webWorkers)}
                <span>Web Workers</span>
              </div>
              <div className="flex items-center gap-2">
                {getFeatureIcon(features.sharedWorkers)}
                <span>Shared Workers</span>
              </div>
              <div className="flex items-center gap-2">
                {getFeatureIcon(features.serviceWorkers)}
                <span>Service Workers</span>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <h4 className="font-medium text-sm text-gray-700 dark:text-gray-300">设备API</h4>
            <div className="space-y-1 text-sm">
              <div className="flex items-center gap-2">
                {getFeatureIcon(features.geolocation)}
                <span>地理位置</span>
              </div>
              <div className="flex items-center gap-2">
                {getFeatureIcon(features.vibration)}
                <span>振动</span>
              </div>
              <div className="flex items-center gap-2">
                {getFeatureIcon(features.battery)}
                <span>电池状态</span>
              </div>
              <div className="flex items-center gap-2">
                {getFeatureIcon(features.gamepad)}
                <span>游戏手柄</span>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <h4 className="font-medium text-sm text-gray-700 dark:text-gray-300">通信技术</h4>
            <div className="space-y-1 text-sm">
              <div className="flex items-center gap-2">
                {getFeatureIcon(features.webRTC)}
                <span>WebRTC</span>
              </div>
              <div className="flex items-center gap-2">
                {getFeatureIcon(features.notifications)}
                <span>通知</span>
              </div>
            </div>
          </div>

          <div className="space-y-2">
            <h4 className="font-medium text-sm text-gray-700 dark:text-gray-300">媒体技术</h4>
            <div className="space-y-1 text-sm">
              <div className="flex items-center gap-2">
                {getFeatureIcon(features.webAudio)}
                <span>Web Audio</span>
              </div>
              <div className="flex items-center gap-2">
                {getFeatureIcon(features.speechSynthesis)}
                <span>语音合成</span>
              </div>
              <div className="flex items-center gap-2">
                {getFeatureIcon(features.speechRecognition)}
                <span>语音识别</span>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <p className="text-gray-500 dark:text-gray-400">HTML5特性检测中...</p>
      )}
    </InfoCard>
  );
}

interface BatteryCardProps {
  batteryInfo: BatteryInfo | null;
  loading?: boolean;
}

export function BatteryCard({ batteryInfo, loading }: BatteryCardProps) {
  const formatTime = (seconds: number) => {
    if (!isFinite(seconds) || seconds === 0) return "未知";
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    return `${hours}小时${minutes}分钟`;
  };

  return (
    <InfoCard title="电池信息">
      <div className="flex items-center gap-2 mb-4">
        <Battery className="h-5 w-5 text-green-500" />
        <span className="font-medium">设备电池状态</span>
      </div>
      {loading ? (
        <div className="animate-pulse space-y-3">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
          ))}
        </div>
      ) : batteryInfo ? (
        batteryInfo.supported ? (
          <div className="space-y-3">
            <InfoItem 
              label="电量" 
              value={`${batteryInfo.level}%`} 
            />
            <InfoItem 
              label="充电状态" 
              value={batteryInfo.charging ? "充电中" : "未充电"} 
            />
            <InfoItem 
              label="充电时间" 
              value={formatTime(batteryInfo.chargingTime)} 
            />
            <InfoItem 
              label="放电时间" 
              value={formatTime(batteryInfo.dischargingTime)} 
            />
          </div>
        ) : (
          <p className="text-gray-500 dark:text-gray-400">此设备不支持电池API</p>
        )
      ) : (
        <p className="text-gray-500 dark:text-gray-400">电池信息加载中...</p>
      )}
    </InfoCard>
  );
}