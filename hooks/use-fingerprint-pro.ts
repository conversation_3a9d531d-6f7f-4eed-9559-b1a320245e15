"use client";

import { useVisitorData } from "@fingerprintjs/fingerprintjs-pro-react";
import { useEffect, useState } from "react";
import { FingerprintProData, FingerprintProExtendedResult } from "@/types";

export function useFingerprintPro() {
  const [fingerprintProData, setFingerprintProData] = useState<FingerprintProData>({
    isLoading: true
  });

  const { 
    data, 
    isLoading, 
    error, 
    getData 
  } = useVisitorData(
    {
      extendedResult: true,
      linkedId: typeof window !== 'undefined' ? window.location.hostname : undefined,
      tag: {
        userAction: 'page_visit',
        timestamp: new Date().toISOString()
      }
    },
    { immediate: true }
  );

  useEffect(() => {
    if (isLoading) {
      setFingerprintProData({ isLoading: true });
      return;
    }

    if (error) {
      setFingerprintProData({
        isLoading: false,
        error: {
          message: error.message || '获取Fingerprint Pro数据时发生错误',
          code: (error as any)?.code || 'UNKNOWN_ERROR'
        }
      });
      return;
    }

    if (data) {
      setFingerprintProData({
        isLoading: false,
        result: data as any
      });
    }
  }, [data, isLoading, error]);

  const refresh = async () => {
    setFingerprintProData({ isLoading: true });
    try {
      const newData = await getData({ ignoreCache: true });
      setFingerprintProData({
        isLoading: false,
        result: newData as any
      });
    } catch (err) {
      setFingerprintProData({
        isLoading: false,
        error: {
          message: (err as Error).message || '刷新数据时发生错误',
          code: 'REFRESH_ERROR'
        }
      });
    }
  };

  return {
    data: fingerprintProData,
    refresh,
    isAvailable: !error && !!process.env.NEXT_PUBLIC_FINGERPRINT_API_KEY
  };
}

export function formatConfidenceScore(score: number): string {
  if (score >= 0.9) return '极高';
  if (score >= 0.8) return '高';
  if (score >= 0.6) return '中等';
  if (score >= 0.4) return '较低';
  return '低';
}

export function formatTimestamp(timestamp: string | null): string {
  if (!timestamp) return '未知';
  
  try {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor(diffMs / (1000 * 60));

    if (diffDays > 0) {
      return `${diffDays}天前`;
    } else if (diffHours > 0) {
      return `${diffHours}小时前`;
    } else if (diffMinutes > 0) {
      return `${diffMinutes}分钟前`;
    } else {
      return '刚刚';
    }
  } catch {
    return '无效时间';
  }
}

export function getRiskLevel(
  incognito: boolean,
  vpn?: { result: boolean },
  proxy?: { result: boolean },
  tor?: { result: boolean },
  tampering?: { result: boolean },
  suspectScore?: number
): 'low' | 'medium' | 'high' {
  const risks = [
    incognito,
    vpn?.result,
    proxy?.result,
    tor?.result,
    tampering?.result,
    (suspectScore || 0) > 50
  ].filter(Boolean).length;

  if (risks >= 3) return 'high';
  if (risks >= 1) return 'medium';
  return 'low';
}