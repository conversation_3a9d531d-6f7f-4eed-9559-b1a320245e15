# CLAUDE.md

这个文件为 Claude Code (claude.ai/code) 在此仓库中工作时提供指导。

## 项目架构

这是一个使用 Next.js 15 和 TypeScript 构建的现代 React 应用，专注于 IP 检测功能。

### 技术栈
- **框架**: Next.js 15 (App Router)
- **语言**: TypeScript
- **样式**: Tailwind CSS v4
- **组件库**: shadcn/ui 组件配置 (New York 风格)
- **工具**: ESLint、Turbopack (开发模式)

### 目录结构
- `app/` - Next.js App Router 主要应用代码
  - `layout.tsx` - 根布局，包含字体配置 (Geist Sans & Mono)
  - `page.tsx` - 主页面组件
  - `globals.css` - 全局样式
- `lib/` - 共享工具函数
  - `utils.ts` - 包含 shadcn/ui 的 `cn` 样式合并函数
- `components/` - 可重用组件 (通过 shadcn/ui 配置)
- `public/` - 静态资源

## 开发命令

```bash
# 开发服务器 (使用 Turbopack)
npm run dev

# 生产构建
npm run build

# 生产服务器
npm run start

# 代码检查
npm run lint
```

## 开发须知

### 样式系统
- 使用 Tailwind CSS v4 作为主要样式系统
- 配置了 shadcn/ui 组件库，使用 New York 风格
- 组件别名：`@/components`、`@/lib/utils`、`@/ui`
- 图标库：Lucide React

### 路径别名
- `@/*` 指向项目根目录
- TypeScript 配置支持绝对路径导入

### 代码标准
- 使用 Next.js 和 TypeScript 的 ESLint 配置
- 严格的 TypeScript 设置
- 支持 JSX 和现代 ES 特性

### 字体配置
- 主字体：Geist Sans (`--font-geist-sans`)
- 等宽字体：Geist Mono (`--font-geist-mono`)