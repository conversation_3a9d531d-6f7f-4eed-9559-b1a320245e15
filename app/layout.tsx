import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono } from "next/font/google";
import { ThemeProvider } from "next-themes";
import { FpjsProvider } from "@fingerprintjs/fingerprintjs-pro-react";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata: Metadata = {
  title: "IP信息检测 - 检测您的网络和设备信息",
  description: "检测您的IP地址、地理位置、设备信息和浏览器指纹等网络信息",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const apiKey = process.env.NEXT_PUBLIC_FINGERPRINT_API_KEY;
  
  return (
    <html lang="zh-CN" suppressHydrationWarning>
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          {apiKey ? (
            <FpjsProvider
              loadOptions={{
                apiKey,
                region: "ap"
              }}
            >
              {children}
            </FpjsProvider>
          ) : (
            children
          )}
        </ThemeProvider>
      </body>
    </html>
  );
}
