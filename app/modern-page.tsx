"use client";

import { useState, useEffect, useCallback } from "react";
import { useEnhancedFingerprint } from "@/hooks/use-enhanced-fingerprint";
import { useDeviceInfo } from "@/hooks/use-device-info";
import { useWebRTCInfo } from "@/hooks/use-webrtc-info";
import { IpInfo, HTTPHeaders } from "@/types";
import {
  Globe,
  MapPin,
  Activity,
  Eye,
  Server,
  Copy,
  CheckCircle,
  Shield,
  Monitor,
  Wifi,
  Clock,
  Download,
  Upload,
  Gauge,
  RefreshCw,
  AlertTriangle,
  Loader2,
  Signal,
  Router,
  Smartphone
} from "lucide-react";

// 数据状态接口
interface DataState<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  lastUpdated: Date | null;
}

export default function ModernNetworkDetection() {
  const [ipInfo, setIpInfo] = useState<DataState<IpInfo>>({
    data: null,
    loading: true,
    error: null,
    lastUpdated: null
  });

  const [httpHeaders, setHttpHeaders] = useState<DataState<HTTPHeaders>>({
    data: null,
    loading: true,
    error: null,
    lastUpdated: null
  });

  const [copied, setCopied] = useState(false);
  const [refreshing, setRefreshing] = useState(false);

  const enhancedFingerprint = useEnhancedFingerprint();
  const deviceInfo = useDeviceInfo();
  const webrtcInfo = useWebRTCInfo();

  // 获取 IP 信息
  const fetchIpInfo = useCallback(async () => {
    setIpInfo(prev => ({ ...prev, loading: true, error: null }));
    try {
      const response = await fetch("/api/ip-info");
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      const data = await response.json();
      setIpInfo({
        data,
        loading: false,
        error: null,
        lastUpdated: new Date()
      });
    } catch (error) {
      console.error("Error fetching IP info:", error);
      setIpInfo(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : "获取IP信息失败"
      }));
    }
  }, []);

  // 获取 HTTP 请求头
  const fetchHttpHeaders = useCallback(async () => {
    setHttpHeaders(prev => ({ ...prev, loading: true, error: null }));
    try {
      const response = await fetch("/api/http-headers");
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      const data = await response.json();
      setHttpHeaders({
        data,
        loading: false,
        error: null,
        lastUpdated: new Date()
      });
    } catch (error) {
      console.error("Error fetching HTTP headers:", error);
      setHttpHeaders(prev => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : "获取HTTP请求头失败"
      }));
    }
  }, []);

  // 刷新所有数据
  const refreshAllData = useCallback(async () => {
    setRefreshing(true);
    try {
      await Promise.all([
        fetchIpInfo(),
        fetchHttpHeaders(),
        enhancedFingerprint.refresh()
      ]);
    } finally {
      setRefreshing(false);
    }
  }, [fetchIpInfo, fetchHttpHeaders, enhancedFingerprint]);

  // 初始化数据加载
  useEffect(() => {
    fetchIpInfo();
    fetchHttpHeaders();
  }, [fetchIpInfo, fetchHttpHeaders]);

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  // 计算网络质量评分
  const getNetworkQuality = useCallback(() => {
    if (!webrtcInfo?.connection) return { score: 0, level: 'unknown' as const };

    const latency = webrtcInfo.connection.rtt || 0;
    const effectiveType = webrtcInfo.connection.effectiveType;
    const downlink = webrtcInfo.connection.downlink || 0;

    let score = 100;

    // 基于延迟评分
    if (latency > 200) score -= 40;
    else if (latency > 100) score -= 25;
    else if (latency > 50) score -= 10;

    // 基于连接类型评分
    if (effectiveType === 'slow-2g') score -= 50;
    else if (effectiveType === '2g') score -= 35;
    else if (effectiveType === '3g') score -= 20;
    else if (effectiveType === '4g') score -= 5;

    // 基于下载速度评分
    if (downlink < 0.5) score -= 30;
    else if (downlink < 1) score -= 20;
    else if (downlink < 2) score -= 10;

    const finalScore = Math.max(score, 0);

    let level: 'excellent' | 'good' | 'fair' | 'poor' | 'unknown';
    if (finalScore >= 90) level = 'excellent';
    else if (finalScore >= 70) level = 'good';
    else if (finalScore >= 50) level = 'fair';
    else level = 'poor';

    return { score: finalScore, level };
  }, [webrtcInfo]);

  const networkQuality = getNetworkQuality();

  // 获取网络质量颜色
  const getQualityColor = (level: string) => {
    switch (level) {
      case 'excellent': return 'text-green-400';
      case 'good': return 'text-blue-400';
      case 'fair': return 'text-yellow-400';
      case 'poor': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  // 获取网络质量文本
  const getQualityText = (level: string) => {
    switch (level) {
      case 'excellent': return '优秀';
      case 'good': return '良好';
      case 'fair': return '一般';
      case 'poor': return '较差';
      default: return '未知';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white">
      {/* 顶部导航栏 */}
      <header className="border-b border-white/10 backdrop-blur-sm bg-black/20">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Shield className="h-8 w-8 text-blue-400" />
              <h1 className="text-2xl font-bold">网络检测中心</h1>
            </div>
            <div className="flex items-center gap-4">
              <button
                onClick={refreshAllData}
                disabled={refreshing}
                className="flex items-center gap-2 px-3 py-1.5 rounded-lg bg-white/10 hover:bg-white/20 transition-colors disabled:opacity-50"
                title="刷新所有数据"
              >
                <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
                <span className="text-sm">刷新</span>
              </button>
              <div className="text-sm text-gray-400">
                <Clock className="h-4 w-4 inline mr-1" />
                <span suppressHydrationWarning>
                  {new Date().toLocaleString()}
                </span>
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* IP 地址主显示区域 */}
        <div className="text-center mb-12">
          <div className="mb-4">
            <h2 className="text-lg text-gray-400 mb-2">我的IP地址</h2>
            <div className="flex items-center justify-center gap-4">
              {ipInfo.loading ? (
                <div className="flex items-center gap-2">
                  <Loader2 className="h-6 w-6 animate-spin text-blue-400" />
                  <span className="ip-display text-gray-400">获取中...</span>
                </div>
              ) : ipInfo.error ? (
                <div className="flex items-center gap-2">
                  <AlertTriangle className="h-6 w-6 text-red-400" />
                  <span className="text-red-400">获取失败</span>
                </div>
              ) : (
                <>
                  <span className="ip-display">
                    {ipInfo.data?.ip || "未知"}
                  </span>
                  {ipInfo.data?.ip && (
                    <button
                      onClick={() => copyToClipboard(ipInfo.data!.ip)}
                      className="p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors"
                      title="复制IP地址"
                    >
                      {copied ? (
                        <CheckCircle className="h-5 w-5 text-green-400" />
                      ) : (
                        <Copy className="h-5 w-5" />
                      )}
                    </button>
                  )}
                </>
              )}
            </div>
          </div>

          {ipInfo.data && !ipInfo.loading && !ipInfo.error && (
            <div className="location-info justify-center">
              <MapPin className="h-5 w-5 text-blue-400" />
              <span>
                {ipInfo.data.country} {ipInfo.data.region && `/ ${ipInfo.data.region}`}
              </span>
            </div>
          )}

          {ipInfo.error && (
            <div className="mt-2 text-sm text-red-400">
              {ipInfo.error}
            </div>
          )}
        </div>

        {/* 主要指标卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {/* 网络质量 */}
          <div className="network-card">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <Signal className="h-5 w-5 text-blue-400" />
                <span className="font-medium">网络质量</span>
              </div>
              <span className={`status-indicator ${
                networkQuality.level === 'excellent' ? 'status-safe' :
                networkQuality.level === 'good' ? 'status-safe' :
                networkQuality.level === 'fair' ? 'status-warning' :
                networkQuality.level === 'poor' ? 'status-danger' : 'bg-gray-500/20 text-gray-400'
              }`}>
                {getQualityText(networkQuality.level)}
              </span>
            </div>

            <div className="relative w-24 h-24 mx-auto mb-4">
              <svg className="w-24 h-24 progress-ring">
                <circle
                  cx="48"
                  cy="48"
                  r="40"
                  stroke="currentColor"
                  strokeWidth="8"
                  fill="transparent"
                  className="text-gray-700"
                />
                <circle
                  cx="48"
                  cy="48"
                  r="40"
                  stroke="currentColor"
                  strokeWidth="8"
                  fill="transparent"
                  strokeDasharray={`${2 * Math.PI * 40}`}
                  strokeDashoffset={`${2 * Math.PI * 40 * (1 - networkQuality.score / 100)}`}
                  className={`${getQualityColor(networkQuality.level)} transition-all duration-1000`}
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-2xl font-bold">{Math.round(networkQuality.score)}%</span>
              </div>
            </div>

            <div className="text-center text-sm text-gray-400">
              网络评分: {Math.round(networkQuality.score)}/100
            </div>

            {webrtcInfo?.connection && (
              <div className="mt-3 text-xs text-gray-500 text-center">
                延迟: {webrtcInfo.connection.rtt || '--'}ms
              </div>
            )}
          </div>

          {/* 下载速度 */}
          <div className="network-card">
            <div className="flex items-center gap-2 mb-4">
              <Download className="h-5 w-5 text-blue-400" />
              <span className="font-medium">下载速度</span>
            </div>

            <div className="text-center">
              <div className="data-value-large text-blue-400 mb-2">
                {webrtcInfo?.connection?.downlink ?
                  `${webrtcInfo.connection.downlink} Mbps` :
                  '--'
                }
              </div>
              <div className="text-sm text-gray-400">
                有效类型: {webrtcInfo?.connection?.effectiveType || '未知'}
              </div>
              {webrtcInfo?.connection?.saveData && (
                <div className="text-xs text-yellow-400 mt-1">
                  省流量模式
                </div>
              )}
            </div>
          </div>

          {/* 延迟 */}
          <div className="network-card">
            <div className="flex items-center gap-2 mb-4">
              <Gauge className="h-5 w-5 text-yellow-400" />
              <span className="font-medium">网络延迟</span>
            </div>

            <div className="text-center">
              <div className={`data-value-large mb-2 ${
                !webrtcInfo?.connection?.rtt ? 'text-gray-400' :
                webrtcInfo.connection.rtt < 50 ? 'text-green-400' :
                webrtcInfo.connection.rtt < 100 ? 'text-yellow-400' : 'text-red-400'
              }`}>
                {webrtcInfo?.connection?.rtt ?
                  `${webrtcInfo.connection.rtt} ms` :
                  '--'
                }
              </div>
              <div className="text-sm text-gray-400">
                往返时间
              </div>
              {webrtcInfo?.connection?.rtt && (
                <div className="text-xs text-gray-500 mt-1">
                  {webrtcInfo.connection.rtt < 50 ? '优秀' :
                   webrtcInfo.connection.rtt < 100 ? '良好' :
                   webrtcInfo.connection.rtt < 200 ? '一般' : '较差'}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* 详细信息网格 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 设备信息 */}
          <div className="network-card">
            <h3 className="section-header">
              {deviceInfo?.device?.type === 'mobile' ? (
                <Smartphone className="h-6 w-6 text-purple-400" />
              ) : (
                <Monitor className="h-6 w-6 text-purple-400" />
              )}
              设备信息
            </h3>

            {deviceInfo ? (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="data-label">浏览器</div>
                    <div className="data-value">
                      {deviceInfo.browser?.name || '未知'} {deviceInfo.browser?.version}
                    </div>
                  </div>
                  <div>
                    <div className="data-label">操作系统</div>
                    <div className="data-value">
                      {deviceInfo.os?.name || '未知'} {deviceInfo.os?.version}
                    </div>
                  </div>
                  <div>
                    <div className="data-label">设备类型</div>
                    <div className="data-value">
                      {deviceInfo.device?.type || '未知'}
                    </div>
                  </div>
                  <div>
                    <div className="data-label">屏幕分辨率</div>
                    <div className="data-value">
                      {deviceInfo.screen ? `${deviceInfo.screen.width}×${deviceInfo.screen.height}` : '未知'}
                    </div>
                  </div>
                  <div>
                    <div className="data-label">像素比</div>
                    <div className="data-value">
                      {deviceInfo.screen?.pixelRatio || '未知'}
                    </div>
                  </div>
                  <div>
                    <div className="data-label">色彩深度</div>
                    <div className="data-value">
                      {deviceInfo.screen?.colorDepth ? `${deviceInfo.screen.colorDepth}位` : '未知'}
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="animate-pulse space-y-3">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="h-4 bg-gray-700 rounded"></div>
                ))}
              </div>
            )}
          </div>

          {/* 位置信息 */}
          <div className="network-card">
            <h3 className="section-header">
              <Globe className="h-6 w-6 text-green-400" />
              位置信息
              {ipInfo.lastUpdated && (
                <span className="text-xs text-gray-500 ml-auto">
                  更新于 {ipInfo.lastUpdated.toLocaleTimeString()}
                </span>
              )}
            </h3>

            {ipInfo.loading ? (
              <div className="animate-pulse space-y-3">
                {[...Array(6)].map((_, i) => (
                  <div key={i} className="h-4 bg-gray-700 rounded"></div>
                ))}
              </div>
            ) : ipInfo.error ? (
              <div className="text-center py-8">
                <AlertTriangle className="h-8 w-8 text-red-400 mx-auto mb-2" />
                <p className="text-red-400 mb-2">获取位置信息失败</p>
                <p className="text-sm text-gray-500">{ipInfo.error}</p>
                <button
                  onClick={fetchIpInfo}
                  className="mt-3 px-4 py-2 bg-blue-500/20 text-blue-400 rounded-lg hover:bg-blue-500/30 transition-colors"
                >
                  重试
                </button>
              </div>
            ) : ipInfo.data ? (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="data-label">国家</div>
                    <div className="data-value">{ipInfo.data.country || '未知'}</div>
                  </div>
                  <div>
                    <div className="data-label">地区</div>
                    <div className="data-value">{ipInfo.data.region || '未知'}</div>
                  </div>
                  <div>
                    <div className="data-label">城市</div>
                    <div className="data-value">{ipInfo.data.city || '未知'}</div>
                  </div>
                  <div>
                    <div className="data-label">ISP</div>
                    <div className="data-value">{ipInfo.data.isp || '未知'}</div>
                  </div>
                  <div>
                    <div className="data-label">时区</div>
                    <div className="data-value">{ipInfo.data.timezone || '未知'}</div>
                  </div>
                  <div>
                    <div className="data-label">坐标</div>
                    <div className="data-value">
                      {ipInfo.data.lat && ipInfo.data.lon ?
                        `${ipInfo.data.lat}, ${ipInfo.data.lon}` :
                        '未知'
                      }
                    </div>
                  </div>
                  {ipInfo.data.asn && (
                    <div className="col-span-2">
                      <div className="data-label">ASN</div>
                      <div className="data-value">{ipInfo.data.asn}</div>
                    </div>
                  )}
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-400">
                无法获取位置信息
              </div>
            )}
          </div>
        </div>

        {/* Fingerprint Pro 增强检测 */}
        {enhancedFingerprint.isAvailable && (
          <div className="mt-8">
            <div className="network-card">
              <div className="flex items-center justify-between mb-6">
                <h3 className="section-header">
                  <Shield className="h-6 w-6 text-blue-400" />
                  Fingerprint Pro 增强检测
                  {enhancedFingerprint.data.hasServerData && (
                    <span className="status-indicator status-info ml-4">
                      <Server className="h-3 w-3" />
                      服务端增强
                    </span>
                  )}
                </h3>
                <div className="flex gap-2">
                  {enhancedFingerprint.refreshServerData && enhancedFingerprint.data.hasServerData && (
                    <button
                      onClick={enhancedFingerprint.refreshServerData}
                      disabled={enhancedFingerprint.data.serverData?.isLoading}
                      className="p-1.5 rounded-md bg-white/10 hover:bg-white/20 transition-colors disabled:opacity-50"
                      title="刷新服务端数据"
                    >
                      <Server className={`h-4 w-4 ${enhancedFingerprint.data.serverData?.isLoading ? 'animate-spin' : ''}`} />
                    </button>
                  )}
                  <button
                    onClick={enhancedFingerprint.refresh}
                    disabled={enhancedFingerprint.data.isLoading}
                    className="p-1.5 rounded-md bg-white/10 hover:bg-white/20 transition-colors disabled:opacity-50"
                    title="刷新所有数据"
                  >
                    <RefreshCw className={`h-4 w-4 ${enhancedFingerprint.data.isLoading ? 'animate-spin' : ''}`} />
                  </button>
                </div>
              </div>

              {enhancedFingerprint.data.isLoading ? (
                <div className="animate-pulse space-y-3">
                  {[...Array(8)].map((_, i) => (
                    <div key={i} className="h-4 bg-gray-700 rounded"></div>
                  ))}
                </div>
              ) : enhancedFingerprint.data.clientData?.error ? (
                <div className="text-center py-8">
                  <AlertTriangle className="h-8 w-8 text-red-400 mx-auto mb-2" />
                  <p className="text-red-400 mb-2">Fingerprint Pro 数据获取失败</p>
                  <p className="text-sm text-gray-500">{enhancedFingerprint.data.clientData.error.message}</p>
                  <button
                    onClick={enhancedFingerprint.refresh}
                    className="mt-3 px-4 py-2 bg-blue-500/20 text-blue-400 rounded-lg hover:bg-blue-500/30 transition-colors"
                  >
                    重试
                  </button>
                </div>
              ) : enhancedFingerprint.data.clientData?.result ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {/* 基础识别 */}
                  <div>
                    <h4 className="font-medium text-gray-300 mb-3 flex items-center gap-2">
                      <Eye className="h-4 w-4" />
                      访问者识别
                    </h4>
                    <div className="space-y-2">
                      <div>
                        <div className="data-label">访问者ID</div>
                        <div className="data-value font-mono text-sm break-all">
                          {enhancedFingerprint.data.clientData.result.visitorId}
                        </div>
                      </div>
                      <div>
                        <div className="data-label">请求ID</div>
                        <div className="data-value font-mono text-xs text-gray-400 break-all">
                          {enhancedFingerprint.data.clientData.result.requestId}
                        </div>
                      </div>
                      <div>
                        <div className="data-label">置信度</div>
                        <div className={`data-value ${
                          enhancedFingerprint.data.clientData.result.confidence?.score >= 0.9 ? 'text-green-400' :
                          enhancedFingerprint.data.clientData.result.confidence?.score >= 0.7 ? 'text-yellow-400' :
                          'text-red-400'
                        }`}>
                          {enhancedFingerprint.data.clientData.result.confidence ?
                            `${(enhancedFingerprint.data.clientData.result.confidence.score * 100).toFixed(1)}%` :
                            '未知'
                          }
                        </div>
                      </div>
                      <div>
                        <div className="data-label">访问者状态</div>
                        <div className="data-value">
                          {enhancedFingerprint.data.clientData.result.visitorFound ? '已知访问者' : '新访问者'}
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 风险检测 */}
                  <div>
                    <h4 className="font-medium text-gray-300 mb-3 flex items-center gap-2">
                      <Shield className="h-4 w-4" />
                      风险检测
                      {!enhancedFingerprint.data.hasServerData && (
                        <span className="text-xs text-gray-500">(基础)</span>
                      )}
                    </h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="data-label">隐身模式</span>
                        <span className={`text-sm ${
                          (enhancedFingerprint.data.clientData.result as any)?.incognito ?
                          'text-yellow-400' : 'text-green-400'
                        }`}>
                          {(enhancedFingerprint.data.clientData.result as any)?.incognito ?
                           '是' : '否'}
                        </span>
                      </div>

                      {enhancedFingerprint.data.hasServerData ? (
                        <>
                          <div className="flex justify-between">
                            <span className="data-label">VPN</span>
                            <div className="flex items-center gap-1">
                              <span className={`text-sm ${
                                enhancedFingerprint.data.serverData?.currentVisit?.vpn?.result ?
                                'text-red-400' : 'text-green-400'
                              }`}>
                                {enhancedFingerprint.data.serverData?.currentVisit?.vpn?.result ?
                                 '检测到' : '未检测到'}
                              </span>
                              {enhancedFingerprint.data.serverData?.currentVisit?.vpn?.confidence && (
                                <span className="text-xs text-gray-500">
                                  ({enhancedFingerprint.data.serverData.currentVisit.vpn.confidence})
                                </span>
                              )}
                            </div>
                          </div>
                          <div className="flex justify-between">
                            <span className="data-label">代理</span>
                            <div className="flex items-center gap-1">
                              <span className={`text-sm ${
                                enhancedFingerprint.data.serverData?.currentVisit?.proxy?.result ?
                                'text-red-400' : 'text-green-400'
                              }`}>
                                {enhancedFingerprint.data.serverData?.currentVisit?.proxy?.result ?
                                 '检测到' : '未检测到'}
                              </span>
                              {enhancedFingerprint.data.serverData?.currentVisit?.proxy?.confidence && (
                                <span className="text-xs text-gray-500">
                                  ({enhancedFingerprint.data.serverData.currentVisit.proxy.confidence})
                                </span>
                              )}
                            </div>
                          </div>
                          <div className="flex justify-between">
                            <span className="data-label">Tor</span>
                            <div className="flex items-center gap-1">
                              <span className={`text-sm ${
                                enhancedFingerprint.data.serverData?.currentVisit?.tor?.result ?
                                'text-red-400' : 'text-green-400'
                              }`}>
                                {enhancedFingerprint.data.serverData?.currentVisit?.tor?.result ?
                                 '检测到' : '未检测到'}
                              </span>
                              {enhancedFingerprint.data.serverData?.currentVisit?.tor?.confidence && (
                                <span className="text-xs text-gray-500">
                                  ({enhancedFingerprint.data.serverData.currentVisit.tor.confidence})
                                </span>
                              )}
                            </div>
                          </div>
                          <div className="flex justify-between">
                            <span className="data-label">篡改检测</span>
                            <div className="flex items-center gap-1">
                              <span className={`text-sm ${
                                enhancedFingerprint.data.serverData?.currentVisit?.tampering?.result ?
                                'text-red-400' : 'text-green-400'
                              }`}>
                                {enhancedFingerprint.data.serverData?.currentVisit?.tampering?.result ?
                                 '检测到' : '未检测到'}
                              </span>
                              {enhancedFingerprint.data.serverData?.currentVisit?.tampering?.confidence && (
                                <span className="text-xs text-gray-500">
                                  ({enhancedFingerprint.data.serverData.currentVisit.tampering.confidence})
                                </span>
                              )}
                            </div>
                          </div>
                          {enhancedFingerprint.data.serverData?.currentVisit?.suspectScore !== undefined && (
                            <div className="flex justify-between">
                              <span className="data-label">可疑评分</span>
                              <span className={`text-sm ${
                                enhancedFingerprint.data.serverData.currentVisit.suspectScore > 50 ?
                                'text-red-400' : 'text-green-400'
                              }`}>
                                {enhancedFingerprint.data.serverData.currentVisit.suspectScore}/100
                              </span>
                            </div>
                          )}
                        </>
                      ) : (
                        <div className="text-xs text-gray-500 italic">
                          服务端增强检测数据加载中...
                        </div>
                      )}
                    </div>
                  </div>

                  {/* 访问历史 */}
                  <div>
                    <h4 className="font-medium text-gray-300 mb-3 flex items-center gap-2">
                      <Activity className="h-4 w-4" />
                      访问历史
                    </h4>
                    <div className="space-y-2">
                      <div>
                        <div className="data-label">访问次数</div>
                        <div className="data-value">
                          {enhancedFingerprint.data.serverData?.visitorHistory?.visits.length || 1}
                        </div>
                      </div>
                      <div>
                        <div className="data-label">首次访问</div>
                        <div className="data-value text-sm">
                          {enhancedFingerprint.data.clientData.result.firstSeenAt?.global ?
                            new Date(enhancedFingerprint.data.clientData.result.firstSeenAt.global).toLocaleString() :
                            '刚刚'
                          }
                        </div>
                      </div>
                      <div>
                        <div className="data-label">最近访问</div>
                        <div className="data-value text-sm">
                          {enhancedFingerprint.data.clientData.result.lastSeenAt?.global ?
                            new Date(enhancedFingerprint.data.clientData.result.lastSeenAt.global).toLocaleString() :
                            '刚刚'
                          }
                        </div>
                      </div>

                      {enhancedFingerprint.data.serverData?.visitorHistory?.visits &&
                       enhancedFingerprint.data.serverData.visitorHistory.visits.length > 1 && (
                        <div className="mt-3">
                          <div className="text-xs text-gray-500 mb-2">最近访问记录:</div>
                          <div className="space-y-1 max-h-24 overflow-y-auto">
                            {enhancedFingerprint.data.serverData.visitorHistory.visits.slice(0, 3).map((visit, index) => (
                              <div key={visit.requestId} className="text-xs text-gray-400 p-2 bg-gray-800/50 rounded">
                                <div className="flex justify-between items-center">
                                  <span>{new Date(visit.timestamp * 1000).toLocaleString()}</span>
                                  <span className="text-blue-400">{visit.ip}</span>
                                </div>
                                {visit.browserDetails && (
                                  <div className="text-gray-500 mt-1">
                                    {visit.browserDetails.browserName} {visit.browserDetails.browserVersion}
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-400">
                  Fingerprint Pro 数据加载中...
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
