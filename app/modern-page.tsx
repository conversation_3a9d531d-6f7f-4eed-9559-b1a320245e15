"use client";

import { useState, useEffect } from "react";
import { useEnhancedFingerprint } from "@/hooks/use-enhanced-fingerprint";
import { useDeviceInfo } from "@/hooks/use-device-info";
import { useWebRTCInfo } from "@/hooks/use-webrtc-info";
import { IpInfo, HTTPHeaders } from "@/types";
import { 
  Globe, 
  MapPin, 
  Activity, 
  Eye, 
  Server, 
  Copy, 
  CheckCircle, 
  Shield, 
  Monitor,
  Wifi,
  Clock,
  Download,
  Upload,
  Gauge
} from "lucide-react";

export default function ModernNetworkDetection() {
  const [ipInfo, setIpInfo] = useState<IpInfo | null>(null);
  const [httpHeaders, setHttpHeaders] = useState<HTTPHeaders | null>(null);
  const [loading, setLoading] = useState(true);
  const [copied, setCopied] = useState(false);
  
  const enhancedFingerprint = useEnhancedFingerprint();
  const deviceInfo = useDeviceInfo();
  const webrtcInfo = useWebRTCInfo();

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [ipResponse, headersResponse] = await Promise.all([
          fetch("/api/ip-info"),
          fetch("/api/http-headers")
        ]);

        if (ipResponse.ok) {
          const ipData = await ipResponse.json();
          setIpInfo(ipData);
        }

        if (headersResponse.ok) {
          const headersData = await headersResponse.json();
          setHttpHeaders(headersData);
        }
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy text: ', err);
    }
  };

  // 计算网络质量评分
  const getNetworkQuality = () => {
    if (!webrtcInfo?.connection) return 0;
    // 基于延迟和连接类型计算评分
    const latency = webrtcInfo.connection.rtt || 0;
    const effectiveType = webrtcInfo.connection.effectiveType;
    
    let score = 100;
    if (latency > 100) score -= 30;
    else if (latency > 50) score -= 15;
    
    if (effectiveType === 'slow-2g') score -= 40;
    else if (effectiveType === '2g') score -= 30;
    else if (effectiveType === '3g') score -= 20;
    
    return Math.max(score, 0);
  };

  const networkQuality = getNetworkQuality();

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white">
      {/* 顶部导航栏 */}
      <header className="border-b border-white/10 backdrop-blur-sm bg-black/20">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <Shield className="h-8 w-8 text-blue-400" />
              <h1 className="text-2xl font-bold">网络检测中心</h1>
            </div>
            <div className="flex items-center gap-4">
              <div className="text-sm text-gray-400">
                <Clock className="h-4 w-4 inline mr-1" />
                {new Date().toLocaleString()}
              </div>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-6 py-8">
        {/* IP 地址主显示区域 */}
        <div className="text-center mb-12">
          <div className="mb-4">
            <h2 className="text-lg text-gray-400 mb-2">我的IP地址</h2>
            <div className="flex items-center justify-center gap-4">
              <span className="ip-display">
                {loading ? "获取中..." : ipInfo?.ip || "未知"}
              </span>
              {ipInfo?.ip && (
                <button
                  onClick={() => copyToClipboard(ipInfo.ip)}
                  className="p-2 rounded-lg bg-white/10 hover:bg-white/20 transition-colors"
                  title="复制IP地址"
                >
                  {copied ? (
                    <CheckCircle className="h-5 w-5 text-green-400" />
                  ) : (
                    <Copy className="h-5 w-5" />
                  )}
                </button>
              )}
            </div>
          </div>
          
          {ipInfo && (
            <div className="location-info justify-center">
              <MapPin className="h-5 w-5 text-blue-400" />
              <span>
                {ipInfo.country} {ipInfo.region && `/ ${ipInfo.region}`}
              </span>
            </div>
          )}
        </div>

        {/* 主要指标卡片 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {/* 网络质量 */}
          <div className="network-card">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <Activity className="h-5 w-5 text-green-400" />
                <span className="font-medium">网络质量</span>
              </div>
              <span className="status-indicator status-safe">良好</span>
            </div>
            
            <div className="relative w-24 h-24 mx-auto mb-4">
              <svg className="w-24 h-24 progress-ring">
                <circle
                  cx="48"
                  cy="48"
                  r="40"
                  stroke="currentColor"
                  strokeWidth="8"
                  fill="transparent"
                  className="text-gray-700"
                />
                <circle
                  cx="48"
                  cy="48"
                  r="40"
                  stroke="currentColor"
                  strokeWidth="8"
                  fill="transparent"
                  strokeDasharray={`${2 * Math.PI * 40}`}
                  strokeDashoffset={`${2 * Math.PI * 40 * (1 - networkQuality / 100)}`}
                  className="text-green-400 transition-all duration-1000"
                />
              </svg>
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-2xl font-bold">{Math.round(networkQuality)}%</span>
              </div>
            </div>
            
            <div className="text-center text-sm text-gray-400">
              网络评分: {Math.round(networkQuality)}/100
            </div>
          </div>

          {/* 下载速度 */}
          <div className="network-card">
            <div className="flex items-center gap-2 mb-4">
              <Download className="h-5 w-5 text-blue-400" />
              <span className="font-medium">下载速度</span>
            </div>
            
            <div className="text-center">
              <div className="data-value-large text-blue-400 mb-2">
                {webrtcInfo?.connection?.downlink || '--'} Mbps
              </div>
              <div className="text-sm text-gray-400">
                有效类型: {webrtcInfo?.connection?.effectiveType || '未知'}
              </div>
            </div>
          </div>

          {/* 延迟 */}
          <div className="network-card">
            <div className="flex items-center gap-2 mb-4">
              <Gauge className="h-5 w-5 text-yellow-400" />
              <span className="font-medium">网络延迟</span>
            </div>
            
            <div className="text-center">
              <div className="data-value-large text-yellow-400 mb-2">
                {webrtcInfo?.connection?.rtt || '--'} ms
              </div>
              <div className="text-sm text-gray-400">
                往返时间
              </div>
            </div>
          </div>
        </div>

        {/* 详细信息网格 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* 设备信息 */}
          <div className="network-card">
            <h3 className="section-header">
              <Monitor className="h-6 w-6 text-purple-400" />
              设备信息
            </h3>
            
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <div className="data-label">浏览器</div>
                  <div className="data-value">
                    {deviceInfo?.browser?.name || '未知'} {deviceInfo?.browser?.version}
                  </div>
                </div>
                <div>
                  <div className="data-label">操作系统</div>
                  <div className="data-value">
                    {deviceInfo?.os?.name || '未知'} {deviceInfo?.os?.version}
                  </div>
                </div>
                <div>
                  <div className="data-label">设备类型</div>
                  <div className="data-value">
                    {deviceInfo?.device?.type || '未知'}
                  </div>
                </div>
                <div>
                  <div className="data-label">屏幕分辨率</div>
                  <div className="data-value">
                    {deviceInfo?.screen ? `${deviceInfo.screen.width}×${deviceInfo.screen.height}` : '未知'}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* 位置信息 */}
          <div className="network-card">
            <h3 className="section-header">
              <Globe className="h-6 w-6 text-green-400" />
              位置信息
            </h3>
            
            {loading ? (
              <div className="animate-pulse space-y-3">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="h-4 bg-gray-700 rounded"></div>
                ))}
              </div>
            ) : ipInfo ? (
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <div className="data-label">国家</div>
                    <div className="data-value">{ipInfo.country}</div>
                  </div>
                  <div>
                    <div className="data-label">地区</div>
                    <div className="data-value">{ipInfo.region || '未知'}</div>
                  </div>
                  <div>
                    <div className="data-label">城市</div>
                    <div className="data-value">{ipInfo.city || '未知'}</div>
                  </div>
                  <div>
                    <div className="data-label">ISP</div>
                    <div className="data-value">{ipInfo.isp || '未知'}</div>
                  </div>
                  <div>
                    <div className="data-label">时区</div>
                    <div className="data-value">{ipInfo.timezone || '未知'}</div>
                  </div>
                  <div>
                    <div className="data-label">坐标</div>
                    <div className="data-value">
                      {ipInfo.lat && ipInfo.lon ? `${ipInfo.lat}, ${ipInfo.lon}` : '未知'}
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-center py-8 text-gray-400">
                无法获取位置信息
              </div>
            )}
          </div>
        </div>

        {/* Fingerprint Pro 增强检测 */}
        {enhancedFingerprint.isAvailable && (
          <div className="mt-8">
            <div className="network-card">
              <h3 className="section-header">
                <Shield className="h-6 w-6 text-blue-400" />
                Fingerprint Pro 增强检测
                {enhancedFingerprint.data.hasServerData && (
                  <span className="status-indicator status-info ml-4">
                    <Server className="h-3 w-3" />
                    服务端增强
                  </span>
                )}
              </h3>
              
              {enhancedFingerprint.data.isLoading ? (
                <div className="animate-pulse space-y-3">
                  {[...Array(6)].map((_, i) => (
                    <div key={i} className="h-4 bg-gray-700 rounded"></div>
                  ))}
                </div>
              ) : enhancedFingerprint.data.clientData?.result ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {/* 基础识别 */}
                  <div>
                    <h4 className="font-medium text-gray-300 mb-3 flex items-center gap-2">
                      <Eye className="h-4 w-4" />
                      访问者识别
                    </h4>
                    <div className="space-y-2">
                      <div>
                        <div className="data-label">访问者ID</div>
                        <div className="data-value font-mono text-sm">
                          {enhancedFingerprint.data.clientData.result.visitorId}
                        </div>
                      </div>
                      <div>
                        <div className="data-label">置信度</div>
                        <div className="data-value">
                          {enhancedFingerprint.data.clientData.result.confidence ? 
                            `${(enhancedFingerprint.data.clientData.result.confidence.score * 100).toFixed(1)}%` : 
                            '未知'
                          }
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* 风险检测 */}
                  <div>
                    <h4 className="font-medium text-gray-300 mb-3 flex items-center gap-2">
                      <Shield className="h-4 w-4" />
                      风险检测
                    </h4>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="data-label">VPN</span>
                        <span className={`text-sm ${
                          enhancedFingerprint.data.serverData?.currentVisit?.vpn?.result ? 
                          'text-red-400' : 'text-green-400'
                        }`}>
                          {enhancedFingerprint.data.serverData?.currentVisit?.vpn?.result ? 
                           '检测到' : '未检测到'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="data-label">代理</span>
                        <span className={`text-sm ${
                          enhancedFingerprint.data.serverData?.currentVisit?.proxy?.result ? 
                          'text-red-400' : 'text-green-400'
                        }`}>
                          {enhancedFingerprint.data.serverData?.currentVisit?.proxy?.result ? 
                           '检测到' : '未检测到'}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="data-label">Tor</span>
                        <span className={`text-sm ${
                          enhancedFingerprint.data.serverData?.currentVisit?.tor?.result ? 
                          'text-red-400' : 'text-green-400'
                        }`}>
                          {enhancedFingerprint.data.serverData?.currentVisit?.tor?.result ? 
                           '检测到' : '未检测到'}
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* 访问历史 */}
                  <div>
                    <h4 className="font-medium text-gray-300 mb-3 flex items-center gap-2">
                      <Activity className="h-4 w-4" />
                      访问历史
                    </h4>
                    <div className="space-y-2">
                      <div>
                        <div className="data-label">访问次数</div>
                        <div className="data-value">
                          {enhancedFingerprint.data.serverData?.visitorHistory?.visits.length || 1}
                        </div>
                      </div>
                      <div>
                        <div className="data-label">首次访问</div>
                        <div className="data-value text-sm">
                          {enhancedFingerprint.data.clientData.result.firstSeenAt?.global ? 
                            new Date(enhancedFingerprint.data.clientData.result.firstSeenAt.global).toLocaleString() : 
                            '刚刚'
                          }
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8 text-gray-400">
                  Fingerprint Pro 数据加载中...
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
