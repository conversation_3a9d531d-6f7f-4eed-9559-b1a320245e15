"use client";

import { useState, useEffect } from "react";
import { InfoCard, InfoItem } from "@/components/info-card";
import { ThemeToggle } from "@/components/theme-toggle";
import { TimezoneInfo } from "@/components/timezone-info";
import { 
  HTTPHeadersCard, 
  WebRTCCard, 
  SystemHardwareCard 
} from "@/components/advanced-info-cards";
import { useDeviceInfo } from "@/hooks/use-device-info";
import { useWebRTCInfo } from "@/hooks/use-webrtc-info";
import { useSystemHardware } from "@/hooks/use-system-hardware";
import { IpInfo, HTTPHeaders } from "@/types";
import { Globe, Smartphone, Monitor, Shield } from "lucide-react";

export default function Home() {
  const [ipInfo, setIpInfo] = useState<IpInfo | null>(null);
  const [httpHeaders, setHttpHeaders] = useState<HTTPHeaders | null>(null);
  const [loading, setLoading] = useState(true);
  const [headersLoading, setHeadersLoading] = useState(true);
  
  const deviceInfo = useDeviceInfo();
  const webrtcInfo = useWebRTCInfo();
  const systemHardware = useSystemHardware();

  useEffect(() => {
    const fetchData = async () => {
      try {
        // 并行获取IP信息和HTTP请求头
        const [ipResponse, headersResponse] = await Promise.all([
          fetch("/api/ip-info"),
          fetch("/api/http-headers")
        ]);

        if (ipResponse.ok) {
          const ipData = await ipResponse.json();
          setIpInfo(ipData);
        }

        if (headersResponse.ok) {
          const headersData = await headersResponse.json();
          setHttpHeaders(headersData);
        }
      } catch (error) {
        console.error("获取数据失败:", error);
      } finally {
        setLoading(false);
        setHeadersLoading(false);
      }
    };

    fetchData();
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-8 px-4">
      <ThemeToggle />
      
      <div className="max-w-7xl mx-auto">
        <header className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">
            IP信息检测
          </h1>
          <p className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
            检测您的IP地址、地理位置、设备信息和浏览器指纹等网络信息
          </p>
        </header>

        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {/* 第一行：基础信息 */}
          {/* IP和网络信息 */}
          <InfoCard title="网络信息">
            <div className="flex items-center gap-2 mb-4">
              <Globe className="h-5 w-5 text-blue-500" />
              <span className="font-medium">IP和地理位置</span>
            </div>
            {loading ? (
              <div className="animate-pulse space-y-3">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
              </div>
            ) : (
              <>
                <InfoItem label="IPv4地址" value={ipInfo?.ip} copyable />
                <InfoItem label="IPv6地址" value={ipInfo?.ipv6} copyable />
                <InfoItem label="国家/地区" value={ipInfo?.country} copyable />
                <InfoItem label="城市" value={ipInfo?.city} copyable />
                {ipInfo?.latitude && ipInfo?.longitude && (
                  <InfoItem 
                    label="坐标" 
                    value={`${ipInfo.latitude}, ${ipInfo.longitude}`} 
                    copyable 
                  />
                )}
                <InfoItem label="ISP" value={ipInfo?.isp} copyable />
                <InfoItem label="ASN" value={ipInfo?.asn} copyable />
              </>
            )}
          </InfoCard>

          {/* 时区信息 */}
          <TimezoneInfo timezone={ipInfo?.timezone} />

          {/* 设备信息 */}
          <InfoCard title="设备信息">
            <div className="flex items-center gap-2 mb-4">
              {deviceInfo?.deviceType === "mobile" ? (
                <Smartphone className="h-5 w-5 text-green-500" />
              ) : (
                <Monitor className="h-5 w-5 text-green-500" />
              )}
              <span className="font-medium">硬件和系统</span>
            </div>
            {deviceInfo ? (
              <>
                <InfoItem label="操作系统" value={`${deviceInfo.os} ${deviceInfo.osVersion || ""}`} copyable />
                <InfoItem label="浏览器" value={`${deviceInfo.browser} ${deviceInfo.browserVersion || ""}`} copyable />
                <InfoItem label="设备类型" value={deviceInfo.deviceType} copyable />
                <InfoItem label="屏幕分辨率" value={deviceInfo.screenResolution} copyable />
                <InfoItem label="像素比" value={deviceInfo.pixelRatio} copyable />
                <InfoItem label="色彩深度" value={`${deviceInfo.colorDepth}位`} copyable />
                <InfoItem label="语言" value={deviceInfo.language} copyable />
              </>
            ) : (
              <div className="animate-pulse space-y-3">
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
              </div>
            )}
          </InfoCard>

          {/* 第二行：高级检测 */}
          {/* HTTP请求头 */}
          <HTTPHeadersCard headers={httpHeaders} loading={headersLoading} />

          {/* WebRTC信息 */}
          <WebRTCCard webrtc={webrtcInfo} loading={!webrtcInfo} />

          {/* 系统硬件信息 */}
          <SystemHardwareCard systemInfo={systemHardware} loading={!systemHardware} />


          {/* 隐私声明 */}
          <InfoCard title="隐私声明" className="lg:col-span-2">
            <div className="flex items-center gap-2 mb-4">
              <Shield className="h-5 w-5 text-amber-500" />
              <span className="font-medium">数据使用说明</span>
            </div>
            <div className="text-sm text-gray-600 dark:text-gray-400 space-y-2">
              <p>• 所有信息仅在本地显示，不会被存储或传输到任何服务器</p>
              <p>• IP地理位置信息通过公开API获取</p>
              <p>• WebRTC检测可能会暴露本地和公网IP地址</p>
              <p>• HTTP请求头信息来自浏览器发送的实际请求</p>
              <p>• 本工具完全开源，您可以查看源代码以确认安全性</p>
              <p>• 参考 <a href="https://browserleaks.com/" target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline">BrowserLeaks</a> 等专业检测工具</p>
            </div>
          </InfoCard>
        </div>

        <footer className="mt-12 text-center text-sm text-gray-500 dark:text-gray-400">
          <p>本工具仅供学习和测试使用，请遵守相关法律法规</p>
        </footer>
      </div>
    </div>
  );
}
