import { NextRequest, NextResponse } from "next/server";
import { geolocation, ipAddress } from "@vercel/functions";

export async function GET(request: NextRequest) {
  try {
    // 获取客户端IP
    const ip = ipAddress(request) || request.headers.get('x-forwarded-for') || 'unknown';
    
    // 获取地理位置信息
    const geo = geolocation(request);
    
    // 尝试从第三方API获取更详细的信息
    let detailedInfo = null;
    if (ip !== 'unknown' && !ip.includes('127.0.0.1') && !ip.includes('::1')) {
      try {
        // 使用免费的IP API服务
        const response = await fetch(`http://ip-api.com/json/${ip}?fields=status,message,country,countryCode,region,city,lat,lon,timezone,isp,as,query`, {
          headers: {
            'User-Agent': 'IP Detection Tool'
          }
        });
        
        if (response.ok) {
          detailedInfo = await response.json();
        }
      } catch (error) {
        console.error('获取详细IP信息失败:', error);
      }
    }

    const ipInfo = {
      ip: ip,
      ipv6: request.headers.get('x-forwarded-for-ipv6') || null,
      country: geo?.country || detailedInfo?.country || null,
      countryCode: geo?.countryRegion || detailedInfo?.countryCode || null,
      region: geo?.region || detailedInfo?.region || null,
      city: geo?.city || detailedInfo?.city || null,
      latitude: geo?.latitude || detailedInfo?.lat || null,
      longitude: geo?.longitude || detailedInfo?.lon || null,
      timezone: detailedInfo?.timezone || null,
      isp: detailedInfo?.isp || null,
      asn: detailedInfo?.as || null,
    };

    return NextResponse.json(ipInfo);
  } catch (error) {
    console.error('获取IP信息时出错:', error);
    return NextResponse.json(
      { error: '获取IP信息失败' },
      { status: 500 }
    );
  }
}